version: '3.8'

services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: drmanhlinhmd_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: drmanhlinhmd_wp
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress_secure_2024
      MYSQL_ROOT_PASSWORD: root_secure_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    ports:
      - "3306:3306"
    networks:
      - drmanhlinhmd_network
    command: --default-authentication-plugin=mysql_native_password

  # WordPress Application
  wordpress:
    image: wordpress:6.4-php8.2-apache
    container_name: drmanhlinhmd_wordpress
    restart: unless-stopped
    depends_on:
      - db
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_NAME: drmanhlinhmd_wp
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress_secure_2024
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        /* Headless WordPress Configuration */
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
        
        /* CORS Headers for API */
        define('JWT_AUTH_SECRET_KEY', 'drmanhlinhmd-jwt-secret-key-2024');
        define('JWT_AUTH_CORS_ENABLE', true);
        
        /* Memory and Performance */
        define('WP_MEMORY_LIMIT', '512M');
        define('WP_MAX_MEMORY_LIMIT', '512M');
        
        /* Security */
        define('DISALLOW_FILE_EDIT', true);
        define('AUTOMATIC_UPDATER_DISABLED', true);
    volumes:
      - wordpress_data:/var/www/html
      - ./wordpress/themes:/var/www/html/wp-content/themes
      - ./wordpress/plugins:/var/www/html/wp-content/plugins
      - ./wordpress/uploads:/var/www/html/wp-content/uploads
      - ./wordpress/wp-config-custom.php:/var/www/html/wp-config-custom.php
    ports:
      - "8080:80"
    networks:
      - drmanhlinhmd_network

  # phpMyAdmin for Database Management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:5.2
    container_name: drmanhlinhmd_phpmyadmin
    restart: unless-stopped
    depends_on:
      - db
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_secure_2024
      MYSQL_ROOT_PASSWORD: root_secure_2024
    ports:
      - "8081:80"
    networks:
      - drmanhlinhmd_network

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:1.25-alpine
    container_name: drmanhlinhmd_nginx
    restart: unless-stopped
    depends_on:
      - wordpress
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - drmanhlinhmd_network

  # Redis Cache (Optional for performance)
  redis:
    image: redis:7.2-alpine
    container_name: drmanhlinhmd_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_secure_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - drmanhlinhmd_network

volumes:
  mysql_data:
    driver: local
  wordpress_data:
    driver: local
  redis_data:
    driver: local

networks:
  drmanhlinhmd_network:
    driver: bridge
