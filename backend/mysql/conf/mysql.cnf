[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql

# Character Set and Collation
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# Performance Tuning for WordPress
max_connections = 200
max_user_connections = 180
thread_cache_size = 16
table_open_cache = 4000
table_definition_cache = 1400

# Memory Settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 50

# Query Cache (for older MySQL versions)
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Binary Logging
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Security
bind-address = 0.0.0.0
skip-name-resolve

# WordPress Specific Optimizations
max_allowed_packet = 64M
tmp_table_size = 32M
max_heap_table_size = 32M

# Full-Text Search
ft_min_word_len = 3
ft_stopword_file = ""

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
