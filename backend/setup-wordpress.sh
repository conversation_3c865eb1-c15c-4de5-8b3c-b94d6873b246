#!/bin/bash

# WordPress Setup Script for <PERSON><PERSON> <PERSON>
# This script sets up WordPress with required plugins and configurations

set -e

echo "🏥 Setting up WordPress for Dr<PERSON> <PERSON> MD..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WP_CONTAINER="drmanhlinhmd_wordpress"
DB_CONTAINER="drmanhlinhmd_mysql"
WP_URL="http://localhost:8080"
WP_TITLE="Dr. M<PERSON> MD - <PERSON><PERSON> khoa <PERSON>"
WP_ADMIN_USER="admin"
WP_ADMIN_PASS="admin_secure_2024"
WP_ADMIN_EMAIL="<EMAIL>"

# Function to run WP-CLI commands
wp_cli() {
    docker exec -it $WP_CONTAINER wp --allow-root "$@"
}

# Function to check if containers are running
check_containers() {
    echo -e "${BLUE}Checking if containers are running...${NC}"
    
    if ! docker ps | grep -q $WP_CONTAINER; then
        echo -e "${RED}WordPress container is not running. Please start with: docker-compose up -d${NC}"
        exit 1
    fi
    
    if ! docker ps | grep -q $DB_CONTAINER; then
        echo -e "${RED}MySQL container is not running. Please start with: docker-compose up -d${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ Containers are running${NC}"
}

# Function to wait for WordPress to be ready
wait_for_wordpress() {
    echo -e "${BLUE}Waiting for WordPress to be ready...${NC}"
    
    for i in {1..30}; do
        if curl -s $WP_URL > /dev/null 2>&1; then
            echo -e "${GREEN}✓ WordPress is ready${NC}"
            return 0
        fi
        echo "Waiting... ($i/30)"
        sleep 10
    done
    
    echo -e "${RED}WordPress is not responding after 5 minutes${NC}"
    exit 1
}

# Function to install WordPress core
install_wordpress() {
    echo -e "${BLUE}Installing WordPress core...${NC}"
    
    # Check if WordPress is already installed
    if wp_cli core is-installed 2>/dev/null; then
        echo -e "${YELLOW}WordPress is already installed${NC}"
        return 0
    fi
    
    # Install WordPress
    wp_cli core install \
        --url="$WP_URL" \
        --title="$WP_TITLE" \
        --admin_user="$WP_ADMIN_USER" \
        --admin_password="$WP_ADMIN_PASS" \
        --admin_email="$WP_ADMIN_EMAIL" \
        --skip-email
    
    echo -e "${GREEN}✓ WordPress core installed${NC}"
}

# Function to install required plugins
install_plugins() {
    echo -e "${BLUE}Installing required plugins...${NC}"
    
    # List of required plugins
    plugins=(
        "advanced-custom-fields"
        "woocommerce"
        "jwt-authentication-for-wp-rest-api"
        "wordpress-seo"
        "wp-rest-api-v2-menus"
        "custom-post-type-ui"
        "wp-super-cache"
        "wordfence"
        "contact-form-7"
        "wp-mail-smtp"
        "duplicate-post"
        "regenerate-thumbnails"
    )
    
    for plugin in "${plugins[@]}"; do
        echo "Installing $plugin..."
        wp_cli plugin install $plugin --activate
    done
    
    echo -e "${GREEN}✓ All plugins installed and activated${NC}"
}

# Function to configure WordPress settings
configure_wordpress() {
    echo -e "${BLUE}Configuring WordPress settings...${NC}"
    
    # Set timezone
    wp_cli option update timezone_string "Asia/Ho_Chi_Minh"
    
    # Set date format
    wp_cli option update date_format "d/m/Y"
    wp_cli option update time_format "H:i"
    
    # Set language to Vietnamese
    wp_cli language core install vi
    wp_cli language core activate vi
    
    # Configure permalinks
    wp_cli rewrite structure '/%postname%/'
    wp_cli rewrite flush
    
    # Set blog description
    wp_cli option update blogdescription "Chuyên khoa Da liễu - Điều trị Nám, Mụn, Sẹo và Chống lão hóa"
    
    # Configure media settings
    wp_cli option update thumbnail_size_w 300
    wp_cli option update thumbnail_size_h 300
    wp_cli option update medium_size_w 768
    wp_cli option update medium_size_h 768
    wp_cli option update large_size_w 1024
    wp_cli option update large_size_h 1024
    
    echo -e "${GREEN}✓ WordPress settings configured${NC}"
}

# Function to create custom post types
create_custom_post_types() {
    echo -e "${BLUE}Creating custom post types...${NC}"
    
    # Services post type
    wp_cli post-type create services \
        --label="Dịch vụ" \
        --public=true \
        --show_in_rest=true \
        --supports=title,editor,thumbnail,excerpt,custom-fields \
        --menu_icon=dashicons-heart
    
    # Testimonials post type
    wp_cli post-type create testimonials \
        --label="Nhận xét khách hàng" \
        --public=true \
        --show_in_rest=true \
        --supports=title,editor,thumbnail,custom-fields \
        --menu_icon=dashicons-format-quote
    
    # Case Studies post type
    wp_cli post-type create case_studies \
        --label="Ca điều trị" \
        --public=true \
        --show_in_rest=true \
        --supports=title,editor,thumbnail,gallery,custom-fields \
        --menu_icon=dashicons-analytics
    
    # Treatments post type
    wp_cli post-type create treatments \
        --label="Phương pháp điều trị" \
        --public=true \
        --show_in_rest=true \
        --supports=title,editor,thumbnail,excerpt,custom-fields \
        --menu_icon=dashicons-admin-tools
    
    echo -e "${GREEN}✓ Custom post types created${NC}"
}

# Function to configure WooCommerce
configure_woocommerce() {
    echo -e "${BLUE}Configuring WooCommerce...${NC}"
    
    # Set currency to VND
    wp_cli option update woocommerce_currency "VND"
    wp_cli option update woocommerce_currency_pos "right_space"
    wp_cli option update woocommerce_price_thousand_sep ","
    wp_cli option update woocommerce_price_decimal_sep "."
    wp_cli option update woocommerce_price_num_decimals 0
    
    # Set Vietnam as default country
    wp_cli option update woocommerce_default_country "VN"
    wp_cli option update woocommerce_store_address "213 Nguyễn Hồng Đào"
    wp_cli option update woocommerce_store_address_2 "Phường 14, Quận Tân Bình"
    wp_cli option update woocommerce_store_city "TP. Hồ Chí Minh"
    wp_cli option update woocommerce_store_postcode "700000"
    
    # Configure shipping
    wp_cli option update woocommerce_ship_to_countries "specific"
    wp_cli option update woocommerce_specific_ship_to_countries '["VN"]'
    
    # Enable REST API
    wp_cli option update woocommerce_api_enabled "yes"
    
    echo -e "${GREEN}✓ WooCommerce configured${NC}"
}

# Function to create sample content
create_sample_content() {
    echo -e "${BLUE}Creating sample content...${NC}"
    
    # Create sample services
    wp_cli post create \
        --post_type=services \
        --post_title="Điều trị Nám" \
        --post_content="Phương pháp điều trị nám hiệu quả với công nghệ White Skin Pro từ Mỹ." \
        --post_status=publish \
        --meta_input='{"service_price":"2000000","service_duration":"60","service_category":"facial"}'
    
    wp_cli post create \
        --post_type=services \
        --post_title="Điều trị Mụn" \
        --post_content="Liệu pháp điều trị mụn toàn diện với Green Laser." \
        --post_status=publish \
        --meta_input='{"service_price":"1500000","service_duration":"45","service_category":"acne"}'
    
    # Create sample testimonial
    wp_cli post create \
        --post_type=testimonials \
        --post_title="Chị Lan - TP.HCM" \
        --post_content="Sau 3 tháng điều trị nám tại phòng khám, da tôi đã cải thiện rõ rệt." \
        --post_status=publish \
        --meta_input='{"patient_age":"35","treatment_type":"melasma","rating":"5"}'
    
    echo -e "${GREEN}✓ Sample content created${NC}"
}

# Function to configure security
configure_security() {
    echo -e "${BLUE}Configuring security settings...${NC}"
    
    # Remove default admin user if exists
    if wp_cli user get admin 2>/dev/null; then
        wp_cli user delete admin --yes
    fi
    
    # Create new admin user with strong password
    wp_cli user <NAME_EMAIL> \
        --role=administrator \
        --user_pass="$WP_ADMIN_PASS" \
        --display_name="Dr. Mạnh Linh MD" \
        --first_name="Mạnh Linh" \
        --last_name="Dr."
    
    # Remove unnecessary themes
    wp_cli theme delete twentytwentyone twentytwentytwo twentytwentythree 2>/dev/null || true
    
    # Remove unnecessary plugins
    wp_cli plugin delete hello akismet 2>/dev/null || true
    
    echo -e "${GREEN}✓ Security configured${NC}"
}

# Main execution
main() {
    echo -e "${GREEN}🏥 Dr. Mạnh Linh MD WordPress Setup${NC}"
    echo "=================================="
    
    check_containers
    wait_for_wordpress
    install_wordpress
    install_plugins
    configure_wordpress
    create_custom_post_types
    configure_woocommerce
    create_sample_content
    configure_security
    
    echo ""
    echo -e "${GREEN}✅ WordPress setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}Access Information:${NC}"
    echo "WordPress URL: $WP_URL"
    echo "Admin URL: $WP_URL/wp-admin"
    echo "Username: drmanhlinhmd"
    echo "Password: $WP_ADMIN_PASS"
    echo ""
    echo -e "${BLUE}Database Access:${NC}"
    echo "phpMyAdmin: http://localhost:8081"
    echo "Username: root"
    echo "Password: root_secure_2024"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Configure ACF field groups"
    echo "2. Import content from original site"
    echo "3. Setup Next.js frontend"
    echo "4. Configure API endpoints"
}

# Run main function
main "$@"
