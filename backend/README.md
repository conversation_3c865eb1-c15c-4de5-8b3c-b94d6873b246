# Backend Setup - <PERSON><PERSON> <PERSON> MD
## WordPress + MySQL + Docker Configuration

This directory contains the complete backend setup for the Dr. <PERSON><PERSON> Linh MD website rebuild project.

---

## 🏗️ Architecture Overview

```
Backend Stack:
├── WordPress 6.4+ (Headless CMS)
├── MySQL 8.0 (Database)
├── phpMyAdmin (Database Management)
├── Nginx (Reverse Proxy)
├── Redis (Caching - Optional)
└── Docker Compose (Orchestration)
```

---

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Docker Compose v2.0+
- At least 4GB RAM available
- Ports 80, 443, 3306, 6379, 8080, 8081 available

### 1. Start the Backend Services
```bash
# Navigate to backend directory
cd backend

# Start all services
docker-compose up -d

# Check if all containers are running
docker-compose ps
```

### 2. Run WordPress Setup Script
```bash
# Make the setup script executable
chmod +x setup-wordpress.sh

# Run the setup script
./setup-wordpress.sh
```

### 3. Access the Services
- **WordPress Admin**: http://localhost:8080/wp-admin
- **WordPress Site**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081
- **WordPress API**: http://localhost:8080/wp-json/wp/v2/

---

## 🔧 Configuration Details

### WordPress Configuration
- **Version**: 6.4+ (Latest stable)
- **Language**: Vietnamese (vi)
- **Timezone**: Asia/Ho_Chi_Minh
- **Memory Limit**: 512M
- **Upload Limit**: 64M
- **Debug Mode**: Enabled (development)

### Database Configuration
- **Engine**: MySQL 8.0
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_unicode_ci
- **Buffer Pool**: 256M
- **Max Connections**: 200

### Security Settings
- **Admin User**: drmanhlinhmd
- **Admin Password**: admin_secure_2024
- **Database Password**: wordpress_secure_2024
- **JWT Secret**: drmanhlinhmd-jwt-secret-key-2024

---

## 📦 Installed Plugins

### Essential Plugins
- **Advanced Custom Fields (ACF)** - Custom field management
- **WooCommerce** - E-commerce functionality
- **JWT Authentication** - API authentication
- **Yoast SEO** - SEO optimization
- **Custom Post Type UI** - Custom content types

### Performance Plugins
- **WP Super Cache** - Page caching
- **Redis Object Cache** - Database caching (if Redis enabled)

### Security Plugins
- **Wordfence** - Security monitoring
- **WP Mail SMTP** - Email delivery

### Utility Plugins
- **Contact Form 7** - Contact forms
- **Duplicate Post** - Content duplication
- **Regenerate Thumbnails** - Image optimization

---

## 🏥 Custom Post Types

### Medical Services (`services`)
- **Purpose**: Treatment and service descriptions
- **Fields**: Price, duration, category, before/after images
- **REST API**: Enabled
- **URL Structure**: `/services/treatment-name`

### Patient Testimonials (`testimonials`)
- **Purpose**: Patient reviews and feedback
- **Fields**: Patient info, rating, treatment type
- **REST API**: Enabled
- **URL Structure**: `/testimonials/patient-name`

### Case Studies (`case_studies`)
- **Purpose**: Treatment case documentation
- **Fields**: Before/after photos, treatment details
- **REST API**: Enabled
- **URL Structure**: `/case-studies/case-name`

### Treatment Methods (`treatments`)
- **Purpose**: Detailed treatment explanations
- **Fields**: Methodology, equipment, results
- **REST API**: Enabled
- **URL Structure**: `/treatments/method-name`

---

## 🛒 WooCommerce Configuration

### Store Settings
- **Currency**: Vietnamese Dong (VND)
- **Country**: Vietnam
- **Address**: 213 Nguyễn Hồng Đào, P.14, Tân Bình, TP.HCM
- **Shipping**: Vietnam only
- **Tax**: Not configured (medical services)

### Product Categories
- **Skincare Products** - Professional cosmetics
- **Treatment Packages** - Service bundles
- **Consultation Vouchers** - Appointment bookings

---

## 🔌 API Endpoints

### WordPress REST API
```
Base URL: http://localhost:8080/wp-json/wp/v2/

Core Endpoints:
├── /posts - Blog posts
├── /pages - Static pages
├── /media - Images and files
├── /users - User accounts
└── /comments - User comments

Custom Endpoints:
├── /services - Medical services
├── /testimonials - Patient reviews
├── /case_studies - Treatment cases
└── /treatments - Treatment methods
```

### Custom API Endpoints
```
Base URL: http://localhost:8080/wp-json/drmanhlinhmd/v1/

Medical Endpoints:
├── /services - Get all medical services
├── /testimonials - Get patient testimonials
├── /treatments - Get treatment options
└── /appointments - Booking system (future)
```

### WooCommerce API
```
Base URL: http://localhost:8080/wp-json/wc/v3/

E-commerce Endpoints:
├── /products - Product catalog
├── /orders - Order management
├── /customers - Customer data
└── /coupons - Discount codes
```

---

## 🔒 Security Features

### WordPress Security
- File editing disabled in admin
- Automatic updates disabled
- Strong admin passwords
- Unnecessary plugins removed
- Default themes removed

### Database Security
- Non-root database user
- Strong passwords
- Limited connections
- Backup procedures

### Network Security
- CORS headers configured
- Rate limiting enabled
- SSL ready (production)
- Firewall rules (Nginx)

---

## 📊 Performance Optimization

### Caching Strategy
- **Page Caching**: WP Super Cache
- **Object Caching**: Redis (optional)
- **Database Caching**: MySQL query cache
- **Static Files**: Nginx caching

### Database Optimization
- Optimized MySQL configuration
- Proper indexing
- Query optimization
- Regular maintenance

### Image Optimization
- WebP format support
- Responsive images
- Lazy loading ready
- CDN ready

---

## 🛠️ Development Tools

### Docker Commands
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f wordpress

# Access WordPress container
docker exec -it drmanhlinhmd_wordpress bash

# Access MySQL container
docker exec -it drmanhlinhmd_mysql mysql -u root -p

# Backup database
docker exec drmanhlinhmd_mysql mysqldump -u root -p drmanhlinhmd_wp > backup.sql

# Restore database
docker exec -i drmanhlinhmd_mysql mysql -u root -p drmanhlinhmd_wp < backup.sql
```

### WP-CLI Commands
```bash
# Run WP-CLI in container
docker exec -it drmanhlinhmd_wordpress wp --allow-root [command]

# Examples:
docker exec -it drmanhlinhmd_wordpress wp --allow-root plugin list
docker exec -it drmanhlinhmd_wordpress wp --allow-root user list
docker exec -it drmanhlinhmd_wordpress wp --allow-root post list
```

---

## 🚨 Troubleshooting

### Common Issues

#### WordPress not loading
```bash
# Check container status
docker-compose ps

# Check WordPress logs
docker-compose logs wordpress

# Restart WordPress
docker-compose restart wordpress
```

#### Database connection error
```bash
# Check MySQL status
docker-compose logs db

# Verify database credentials in wp-config.php
# Restart database
docker-compose restart db
```

#### Plugin activation errors
```bash
# Check PHP error logs
docker exec -it drmanhlinhmd_wordpress tail -f /var/log/apache2/error.log

# Increase memory limit in wp-config.php
# Deactivate problematic plugins
```

#### Permission issues
```bash
# Fix WordPress file permissions
docker exec -it drmanhlinhmd_wordpress chown -R www-data:www-data /var/www/html
docker exec -it drmanhlinhmd_wordpress chmod -R 755 /var/www/html
```

---

## 📋 Maintenance Tasks

### Daily
- Monitor container health
- Check error logs
- Verify backup completion

### Weekly
- Update WordPress core
- Update plugins
- Database optimization
- Security scan

### Monthly
- Full system backup
- Performance audit
- Security review
- Plugin cleanup

---

## 🔄 Next Steps

1. **Frontend Setup**: Initialize Next.js project
2. **Content Migration**: Import content from original site
3. **API Integration**: Connect frontend to WordPress API
4. **Testing**: Comprehensive testing of all features
5. **Production**: Deploy to production environment

---

**Setup Date**: 2025-07-28  
**Last Updated**: 2025-07-28  
**Version**: 1.0.0
