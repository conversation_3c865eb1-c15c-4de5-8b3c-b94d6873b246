<?php
/**
 * Custom WordPress Configuration for Dr<PERSON> <PERSON> MD
 * Headless WordPress Setup with API Optimizations
 */

// Security Keys and Salts
define('AUTH_KEY',         'drmanhlinhmd-auth-key-2024-secure-random-string');
define('SECURE_AUTH_KEY',  'drmanhlinhmd-secure-auth-key-2024-random-string');
define('LOGGED_IN_KEY',    'drmanhlinhmd-logged-in-key-2024-random-string');
define('NONCE_KEY',        'drmanhlinhmd-nonce-key-2024-random-string');
define('AUTH_SALT',        'drmanhlinhmd-auth-salt-2024-random-string');
define('SECURE_AUTH_SALT', 'drmanhlinhmd-secure-auth-salt-2024-random');
define('LOGGED_IN_SALT',   'drmanhlinhmd-logged-in-salt-2024-random');
define('NONCE_SALT',       'drmanhlinhmd-nonce-salt-2024-random-string');

// Headless WordPress Configuration
define('HEADLESS_MODE_CLIENT_URL', 'http://localhost:3000');
define('WP_HOME', 'http://localhost:8080');
define('WP_SITEURL', 'http://localhost:8080');

// API and CORS Configuration
define('JWT_AUTH_SECRET_KEY', 'drmanhlinhmd-jwt-secret-key-2024');
define('JWT_AUTH_CORS_ENABLE', true);

// Enable CORS for all origins (development only)
if (!function_exists('add_cors_http_header')) {
    function add_cors_http_header() {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
        header("Access-Control-Allow-Credentials: true");
    }
    add_action('init', 'add_cors_http_header');
}

// REST API Optimizations
define('REST_REQUEST_TIMEOUT', 30);

// Performance Optimizations
define('WP_MEMORY_LIMIT', '512M');
define('WP_MAX_MEMORY_LIMIT', '512M');
define('EMPTY_TRASH_DAYS', 30);
define('WP_POST_REVISIONS', 5);
define('AUTOSAVE_INTERVAL', 300);

// Cache Configuration
define('WP_CACHE', true);
define('ENABLE_CACHE', true);

// Redis Configuration (if using Redis)
define('WP_REDIS_HOST', 'redis');
define('WP_REDIS_PORT', 6379);
define('WP_REDIS_PASSWORD', 'redis_secure_2024');
define('WP_REDIS_TIMEOUT', 1);
define('WP_REDIS_READ_TIMEOUT', 1);
define('WP_REDIS_DATABASE', 0);

// File Upload Settings
define('ALLOW_UNFILTERED_UPLOADS', false);
define('UPLOADS', 'wp-content/uploads');

// Security Settings
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', false);
define('AUTOMATIC_UPDATER_DISABLED', true);
define('WP_AUTO_UPDATE_CORE', false);
define('FORCE_SSL_ADMIN', false); // Set to true in production

// Debug Settings (disable in production)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
define('SAVEQUERIES', true);

// Custom Content Directory
define('WP_CONTENT_DIR', '/var/www/html/wp-content');
define('WP_CONTENT_URL', 'http://localhost:8080/wp-content');

// Multisite (if needed in future)
// define('WP_ALLOW_MULTISITE', true);

// Custom Database Table Prefix
$table_prefix = 'wp_drmanhlinhmd_';

// Increase PHP limits
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);
ini_set('max_input_vars', 3000);
ini_set('upload_max_filesize', '64M');
ini_set('post_max_size', '64M');

// Custom Functions for Headless WordPress
if (!function_exists('enable_headless_features')) {
    function enable_headless_features() {
        // Remove unnecessary WordPress features for headless setup
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');
        
        // Enable additional REST API endpoints
        add_theme_support('post-thumbnails');
        add_theme_support('menus');
        add_theme_support('custom-logo');
        add_theme_support('title-tag');
    }
    add_action('after_setup_theme', 'enable_headless_features');
}

// Custom REST API endpoints for medical content
if (!function_exists('register_custom_rest_routes')) {
    function register_custom_rest_routes() {
        register_rest_route('drmanhlinhmd/v1', '/services', array(
            'methods' => 'GET',
            'callback' => 'get_medical_services',
            'permission_callback' => '__return_true'
        ));
        
        register_rest_route('drmanhlinhmd/v1', '/testimonials', array(
            'methods' => 'GET',
            'callback' => 'get_patient_testimonials',
            'permission_callback' => '__return_true'
        ));
        
        register_rest_route('drmanhlinhmd/v1', '/treatments', array(
            'methods' => 'GET',
            'callback' => 'get_treatment_options',
            'permission_callback' => '__return_true'
        ));
    }
    add_action('rest_api_init', 'register_custom_rest_routes');
}

// Enable WordPress features needed for medical website
if (!function_exists('setup_medical_website_features')) {
    function setup_medical_website_features() {
        // Add support for custom post types
        add_theme_support('custom-fields');
        add_theme_support('post-formats', array('gallery', 'video'));
        
        // Register navigation menus
        register_nav_menus(array(
            'primary' => 'Primary Navigation',
            'footer' => 'Footer Navigation',
            'services' => 'Services Menu'
        ));
    }
    add_action('after_setup_theme', 'setup_medical_website_features');
}

// Optimize WordPress for medical content
if (!function_exists('optimize_for_medical_content')) {
    function optimize_for_medical_content() {
        // Increase excerpt length for medical descriptions
        add_filter('excerpt_length', function() { return 50; });
        
        // Custom excerpt more text
        add_filter('excerpt_more', function() { return '...'; });
        
        // Enable shortcodes in text widgets
        add_filter('widget_text', 'do_shortcode');
    }
    add_action('init', 'optimize_for_medical_content');
}

// Load WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

require_once(ABSPATH . 'wp-settings.php');
